package com.example.domain.useCases

import com.example.common.appConstants.AppConstantSmenGi
import com.example.common.logger.Logger.log
import com.example.domain.utils.EFunnelConstant
import com.example.domain.models.AppConfig
import com.example.domain.models.NetworkStatus
import com.example.domain.models.Realisation
import com.example.domain.models.funnel.FunnelWebParams
import com.example.domain.models.isFlavorOfList
import com.example.domain.repository.CookiesRepository
import com.example.domain.repository.UserRepository
import com.example.domain.useCases.funnel.EmitFunnelWebParamsUseCase
import com.example.domain.utils.Constants
import com.example.domain.utils.Constants.YS_USER_ID_COOKIE
import kotlinx.coroutines.flow.firstOrNull
import java.net.HttpCookie
import java.net.URI

class HandleCookiesUseCase(
    private val userRepository: UserRepository,
    private val cookiesRepository: CookiesRepository,
    private val emitFunnelWebParamsUseCase: EmitFunnelWebParamsUseCase,
    private val getNetworkStatusStreamUseCase: GetNetworkStatusStreamUseCase
) {
    private val flavorsThatNeedHandleCookies = listOf(
        AppConstantSmenGi.VulcanBetCom,
        AppConstantSmenGi.GgBet
    ).map { it.tokenValue }

    suspend operator fun invoke(url: String, appConfig: AppConfig) {
        val cookieString = cookiesRepository.getCookies(url)
        log("HandleCookiesUseCase cookieString url = $url")
        log("HandleCookiesUseCase cookieString = $cookieString")

        val httpCookiesList = cookieString?.split(";")
            ?.map(HttpCookie::parse)
            ?.flatten()

        handleVulcanBetCookies(httpCookiesList, url, appConfig)
        processYellowUserId(appConfig.logic == Realisation.YS.name, httpCookiesList)
        handleDataForFunnel(url, httpCookiesList, appConfig)
    }

    private suspend fun handleVulcanBetCookies(
        httpCookiesList: List<HttpCookie>?,
        url: String,
        appConfig: AppConfig
    ) {
        if (httpCookiesList.isNullOrEmpty()) return
        if (!appConfig.isFlavorOfList(flavorsThatNeedHandleCookies)) return

        val webLoginCookie = httpCookiesList.find { it.name == Constants.PASETO_COOKIE }?.toString()
        val storedLoginCookie = userRepository.getSavedLoginCookie()

        if (webLoginCookie == storedLoginCookie) return

        log("HandleCookiesUseCase webLoginCookie = $webLoginCookie url = $url")

        return when {
            webLoginCookie != null -> {
                userRepository.saveLoginCookie(webLoginCookie)
                cookiesRepository.setCookies(url, webLoginCookie)
            }

            !storedLoginCookie.isNullOrEmpty() -> {
                cookiesRepository.setCookies(url, storedLoginCookie)
            }

            else -> return
        }
    }

    private suspend fun processYellowUserId(isYsApp: Boolean, rawHttpCookie: List<HttpCookie>?) {
        if (!isYsApp) return

        rawHttpCookie?.find { it.name == YS_USER_ID_COOKIE }?.value
            ?.takeIf { it.isNotEmpty() && it != "0" }
            ?.let {
                userRepository.saveUserId(it)
            } ?: userRepository.removeUserId()
    }

    private suspend fun handleDataForFunnel(
        url: String?, cookies: List<HttpCookie>?, appConfig: AppConfig
    ) {
        if (url.isNullOrEmpty()) return
        if (cookies.isNullOrEmpty()) return

        val isYsProject = appConfig.logic == Realisation.YS.name
        if (!isYsProject && !cookies.any { it.name == EFunnelConstant.SITE_REF_CODE.paramName }) {
            return
        }

        val host = URI(url).host
        if (host != null || isNetworkConnected()) {
            emitFunnelWebParamsUseCase(
                funnelWebParams = FunnelWebParams(
                    host = host,
                    userId = getUserIdFromCookies(cookies = cookies),
                    ysUserId = getUserIdYellowFromCookies(cookies = cookies),
                    siteRef = getRefCodeFromCookies(cookies = cookies)
                )
            )
        }
    }

    private suspend fun isNetworkConnected(): Boolean {
        return getNetworkStatusStreamUseCase().firstOrNull() == NetworkStatus.CONNECTED
    }

    private fun getUserIdFromCookies(cookies: List<HttpCookie>): String? {
        return cookies.find { cookie ->
            cookie.name == EFunnelConstant.SITE_REF_USER_ID.paramName
        }?.value?.takeIf { it.isNotEmpty() && it != "0" }
    }

    private fun getUserIdYellowFromCookies(cookies: List<HttpCookie>): String? {
        return cookies.find { cookie ->
            cookie.name == EFunnelConstant.SITE_REF_USER_ID_YS.paramName
        }?.value?.takeIf { it.isNotEmpty() && it != "0" }
    }

    private fun getRefCodeFromCookies(cookies: List<HttpCookie>): String? {
        return cookies.find { cookie ->
            cookie.name == EFunnelConstant.SITE_REF_CODE.paramName
        }?.value
    }
}
