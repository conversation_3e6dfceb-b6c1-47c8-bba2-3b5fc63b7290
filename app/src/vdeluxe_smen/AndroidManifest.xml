<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter android:label="@string/app_name">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />

                <data android:host="vd2.unsubu.com" />
                <data android:host="www.vd2.unsubu.com" />

                <data android:host="vd.unsubu.com" />
                <data android:host="www.vd.unsubu.com" />

                <data android:host="vulkan-deluxe.email" />
                <data android:host="deluxr.org" />
                <data android:host="vde1ux.com " />
                <data android:host="deluxr.club" />
                <data android:host="deluxr.one" />
                <data android:host="vdelux.info" />
                <data android:host="vdlx.one" />
                <data android:host="vdelux.bet" />
                <data android:host="vdlx.xyz" />
                <data android:host="vdlx.site" />
                <data android:host="vlkdlx.email" />
                <data android:host="vlkdeluxe.net" />
                <data android:host="vlkdeluxe.club" />
                <data android:host="vlkdeluxe.org" />
                <data android:host="vulkandlx.com" />
                <data android:host="vulkandlx.org" />
                <data android:host="vulkandlx.net" />
                <data android:host="vulkandlx.email" />
                <data android:host="vulkan-dlx.com" />
                <data android:host="vdlx.online" />
                <data android:host="vdlx.top" />
                <data android:host="vdlx7.top" />
                <data android:host="deluxx.top" />
                <data android:host="re-vdelux.com" />
                <data android:host="vdelux1.org" />
                <data android:host="vdlx.fun" />
                <data android:host="deluxx.one" />
                <data android:host="vdlx.org" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="vdel1.vdeltrack.com" />
                <data android:host="vdel2.vdeltrack.com" />
                <data android:host="vdel3.vdeltrack.com" />
                <data android:host="vdel4.vdeltrack.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Check Pre-Game Urls -->
                <data android:scheme="app" />
                <data android:host="secretapp32.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="social" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="open"
                    android:host="${REAL_APP_ID}" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.example.general.OPEN_PUSH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.example.error.ui.ErrorActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <service
            android:name=".service.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification" />
    </application>

</manifest>
