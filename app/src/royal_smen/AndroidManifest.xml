<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter android:label="@string/app_name">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="vr2.unsubu.com" />
                <data android:host="www.vr2.unsubu.com" />

                <data android:host="vr.unsubu.com" />
                <data android:host="www.vr.unsubu.com" />

                <data android:host="vulkanroyal.email" />
                <data android:host="r-royal.club " />

                <data android:host="goredirect.club" />
                <data android:host="r-royal.club" />
                <data android:host="r-royal.one" />
                <data android:host="r-royal.org" />
                <data android:host="r-royal.site" />
                <data android:host="royal-club.org" />
                <data android:host="royal-club.top" />
                <data android:host="royal-rdr.club" />
                <data android:host="royal-rdr.com" />
                <data android:host="royal-rdr.org" />
                <data android:host="royalclub.top" />
                <data android:host="rroyal.top" />
                <data android:host="vlkredirect.com" />
                <data android:host="vlkroyal.email" />
                <data android:host="vlkroyalclub.org" />
                <data android:host="vroyal.pro" />
                <data android:host="vulcanroyal.email" />
                <data android:host="vu1kanroyal.com" />
                <data android:host="mail-royal.email" />
                <data android:host="mail-royal.net" />
                <data android:host="mail-royal.org" />
                <data android:host="mail-royal.win" />
                <data android:host="mail-vroyal.com" />
                <data android:host="royal-vip1.com" />
                <data android:host="royal-vulk7.com" />
                <data android:host="vr-777.com" />
                <data android:host="vr-game24.com" />
                <data android:host="vulc-rayal666.com" />
                <data android:host="rroyal.club" />
                <data android:host="rroyal.fun" />
                <data android:host="rroyal.org" />
                <data android:host="rroyal.pro" />
                <data android:host="v-royal24.com" />
                <data android:host="vr-top24.com" />
                <data android:host="vroyal-official.com" />
                <data android:host="vroyal-online24.com" />
                <data android:host="vroyal.biz" />
                <data android:host="vulk-royal24.com" />
                <data android:host="vroyal.org" />
                <data android:host="e-clubroyal.com" />
                <data android:host="e-clubroyal.net" />
                <data android:host="email-royal.com" />
                <data android:host="email-royal.net" />
                <data android:host="email-royal.org" />
                <data android:host="klub-royal.com" />
                <data android:host="royal-klub.com" />
                <data android:host="royal-klub.email" />
                <data android:host="royal-klub.net" />
                <data android:host="royal-klub.org" />
                <data android:host="oneredirect.club" />
                <data android:host="royal-red.appspot.com" />
                <data android:host="rroyal.one" />
                <data android:host="vroyal.top" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="v24v1.vfourtrack.com" />
                <data android:host="vr1.vrltrack.com" />
                <data android:host="vr2.vrltrack.com" />
                <data android:host="vr3.vrltrack.com" />
                <data android:host="vr4.vrltrack.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Check Pre-Game Urls -->
                <data android:scheme="app" />
                <data android:host="secretapp33.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="social" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="open"
                    android:host="${REAL_APP_ID}" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.example.general.OPEN_PUSH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.example.error.ui.ErrorActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <service
            android:name=".service.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification" />
    </application>

</manifest>
