<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="338dp"
    android:height="200dp"
    android:viewportWidth="338"
    android:viewportHeight="200">
  <group>
    <clip-path
        android:pathData="M0,0h338v200h-338z"/>
    <path
        android:pathData="M278.19,51.02C278.01,49.74 276.82,48.83 275.54,49.11C274.26,49.29 273.35,50.47 273.62,51.75C273.98,53.94 274.35,60.78 272.06,63.52C271.52,64.16 270.97,64.43 270.14,64.62L270.33,51.57C270.33,49.65 268.86,48.1 266.94,48.1C265.02,48.1 263.47,49.65 263.56,51.57L264.2,76.3C263.38,76.2 262.74,75.84 262.19,75.2C259.81,72.46 260.27,65.62 260.63,63.43C260.82,62.15 259.99,60.97 258.71,60.78C257.43,60.6 256.24,61.42 256.06,62.7C255.88,63.8 254.51,73.38 258.71,78.21C260.18,79.85 262.1,80.86 264.38,80.95L264.66,91.72H269.78L270.14,69.27C272.34,69.09 274.26,68.18 275.72,66.53C279.65,61.7 278.37,52.12 278.19,51.02Z"
        android:fillColor="#D8DCE6"/>
    <path
        android:pathData="M59.35,72.01C59.26,71.19 58.44,70.64 57.61,70.73C56.79,70.82 56.24,71.64 56.33,72.46C56.52,73.83 56.79,78.21 55.33,79.95C54.96,80.31 54.6,80.58 54.14,80.68L54.32,72.37C54.32,71.19 53.41,70.18 52.13,70.18C50.94,70.18 49.93,71.19 49.93,72.37L50.3,88.16C49.75,88.07 49.38,87.88 49.02,87.43C47.55,85.69 47.83,81.31 48.01,79.95C48.1,79.12 47.55,78.39 46.73,78.21C45.91,78.12 45.18,78.67 44.99,79.49C44.9,80.22 43.99,86.24 46.73,89.43C47.65,90.53 48.93,91.08 50.39,91.17L50.57,98.01H53.86L54.14,83.69C55.6,83.59 56.79,82.96 57.71,81.95C60.36,78.76 59.44,72.65 59.35,72.01Z"
        android:fillColor="#D8DCE6"/>
    <path
        android:pathData="M169,199.91C262.34,199.91 338,173.19 338,140.24C338,107.28 262.34,80.57 169,80.57C75.66,80.57 0,107.28 0,140.24C0,173.19 75.66,199.91 169,199.91Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="168.99"
            android:startY="53.53"
            android:endX="168.99"
            android:endY="147.26"
            android:type="linear">
          <item android:offset="0" android:color="#FFD8DCE5"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M210.52,90.05C209.88,90.05 209.24,89.87 208.69,89.51C207.13,88.5 206.68,86.41 207.68,84.85L215.73,72.26C216.74,70.71 218.84,70.26 220.4,71.26C221.95,72.26 222.41,74.36 221.4,75.91L213.35,88.5C212.71,89.51 211.62,90.05 210.52,90.05Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="202.76"
            android:startY="74.02"
            android:endX="220.26"
            android:endY="83.62"
            android:type="linear">
          <item android:offset="0" android:color="#FFA53204"/>
          <item android:offset="1" android:color="#FFE6784D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M222.5,77.1C229.12,77.1 234.48,71.75 234.48,65.15C234.48,58.54 229.12,53.19 222.5,53.19C215.88,53.19 210.52,58.54 210.52,65.15C210.52,71.75 215.88,77.1 222.5,77.1Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M226.72,73.27C229.05,73.27 230.93,71.39 230.93,69.07C230.93,66.75 229.05,64.87 226.72,64.87C224.4,64.87 222.52,66.75 222.52,69.07C222.52,71.39 224.4,73.27 226.72,73.27Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M235.94,64.96C236.03,66.51 235.85,68.07 235.39,69.53C235.21,70.26 234.48,70.62 233.84,70.53L209.88,65.97C209.15,65.88 208.69,65.15 208.78,64.51C209.42,57.39 215.64,51.82 223.05,52.19C229.91,52.46 235.58,58.03 235.94,64.96Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="212.55"
            android:startY="56.18"
            android:endX="230.04"
            android:endY="65.78"
            android:type="linear">
          <item android:offset="0" android:color="#FFA53204"/>
          <item android:offset="1" android:color="#FFE6784D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M204.76,83.58H111.93C106.45,83.58 102.06,87.96 102.06,93.43V95.8C102.06,97.08 103.06,98.18 104.44,98.18H212.35C213.63,98.18 214.73,97.17 214.73,95.8V93.43C214.63,88.05 210.15,83.58 204.76,83.58Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="150.73"
            android:startY="48.65"
            android:endX="156.97"
            android:endY="86.39"
            android:type="linear">
          <item android:offset="0" android:color="#FFA53204"/>
          <item android:offset="1" android:color="#FFE6784D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M186.28,79.93H184.82V72.35C184.82,53.01 169.09,37.32 149.7,37.32C133.15,37.32 114.59,46.9 114.59,70.99C114.59,86.86 126.38,96.44 139.92,96.44C147.6,96.44 154.37,93.07 159.03,87.77H186.28C188.48,87.77 190.22,86.04 190.22,83.85C190.22,81.66 188.39,79.93 186.28,79.93Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:centerX="128.73"
            android:centerY="32.94"
            android:gradientRadius="76.51"
            android:type="radial">
          <item android:offset="0" android:color="#FF62DEFF"/>
          <item android:offset="1" android:color="#FF0882E9"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M141.11,83.58C135.16,83.58 128.76,79.65 128.76,70.99C128.76,60.77 135.26,54.11 145.31,54.11C154.28,54.11 161.59,61.41 161.59,70.35C161.59,71.9 160.31,73.18 158.76,73.18C157.2,73.18 155.92,71.9 155.92,70.35C155.92,64.51 151.17,59.76 145.31,59.76C141.29,59.76 134.43,61.22 134.43,70.99C134.43,77.55 140.01,77.92 141.11,77.92C143.85,77.92 146.14,75.64 146.14,72.9C146.14,71.44 145.5,70.07 144.4,69.16C143.21,68.16 143.12,66.33 144.13,65.15C145.13,63.96 146.96,63.87 148.15,64.87C150.53,66.88 151.81,69.8 151.81,72.9C151.81,78.83 147.05,83.58 141.11,83.58Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M200.37,90.97C199.73,90.97 199.09,90.78 198.54,90.42C196.98,89.42 196.53,87.32 197.53,85.77L205.58,73.18C206.59,71.62 208.69,71.17 210.24,72.17C211.8,73.18 212.26,75.27 211.25,76.82L203.2,89.42C202.56,90.42 201.46,90.97 200.37,90.97Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="216.29"
            android:startY="63.05"
            android:endX="203.48"
            android:endY="82.81"
            android:type="linear">
          <item android:offset="0" android:color="#FFA53204"/>
          <item android:offset="1" android:color="#FFE6784D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M212.35,78.01C218.96,78.01 224.33,72.66 224.33,66.06C224.33,59.46 218.96,54.11 212.35,54.11C205.73,54.11 200.37,59.46 200.37,66.06C200.37,72.66 205.73,78.01 212.35,78.01Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M219.66,70.47C219.66,72.85 217.74,74.67 215.46,74.67C213.08,74.67 211.25,72.76 211.25,70.47C211.25,68.19 213.17,66.28 215.46,66.28C217.74,66.28 219.66,68.19 219.66,70.47Z"
        android:fillColor="#000000"/>
    <path
        android:pathData="M225.79,65.88C225.88,67.43 225.7,68.98 225.24,70.44C225.06,71.17 224.33,71.53 223.69,71.44L199.73,66.88C199,66.79 198.54,66.06 198.63,65.42C199.27,58.3 205.49,52.74 212.9,53.1C219.76,53.38 225.43,58.94 225.79,65.88Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="204.09"
            android:startY="46.21"
            android:endX="210.37"
            android:endY="60.7"
            android:type="linear">
          <item android:offset="0" android:color="#FFA53204"/>
          <item android:offset="1" android:color="#FFE6784D"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M169.55,21.44C169.55,22.17 169.18,22.9 168.63,23.36C167.63,24.18 166.07,24 165.25,22.9C161.96,18.61 157.02,16.24 151.63,16.24C146.23,16.24 141.2,18.7 137.91,22.99C137.08,24.09 135.62,24.27 134.52,23.45C133.43,22.63 133.24,21.17 134.07,20.07C138.27,14.6 144.67,11.41 151.63,11.41C158.48,11.41 164.79,14.51 169,19.98C169.37,20.44 169.55,20.99 169.55,21.44Z"
        android:fillColor="#3BB6F6"/>
    <path
        android:pathData="M160.77,29.01C160.77,29.74 160.4,30.47 159.85,30.93C158.85,31.75 157.29,31.57 156.47,30.47C155.28,28.92 153.54,28.1 151.63,28.1C149.7,28.1 147.88,29.01 146.69,30.57C145.86,31.66 144.4,31.84 143.3,31.02C142.21,30.2 142.02,28.74 142.85,27.65C144.95,24.91 148.15,23.36 151.53,23.36C154.92,23.36 158.12,24.91 160.22,27.65C160.59,27.92 160.77,28.47 160.77,29.01Z"
        android:fillColor="#3BB6F6"/>
    <path
        android:pathData="M176.04,13.14C176.04,13.87 175.68,14.6 175.13,15.05C174.12,15.88 172.57,15.69 171.74,14.6C166.9,8.3 159.58,4.74 151.63,4.74C143.58,4.74 136.26,8.39 131.41,14.78C130.59,15.88 129.13,16.06 128.03,15.24C126.93,14.42 126.75,12.96 127.57,11.86C133.34,4.38 142.11,0 151.63,0C161.04,0 169.73,4.29 175.49,11.68C175.86,12.14 176.04,12.68 176.04,13.14Z"
        android:fillColor="#3BB6F6"/>
  </group>
</vector>
