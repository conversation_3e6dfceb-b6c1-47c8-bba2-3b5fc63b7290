<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter android:label="@string/app_name">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="gmsd2.unsubu.com" />
                <data android:host="www.gmsd2.unsubu.com" />

                <data android:host="www.unsubu.com" />
                <data android:host="unsubu.com" />

                <data android:host="gmsdeluxe.email" />
                <data android:host="go-gmsd.com" />
                <data android:host="play-gmsd.com" />
                <data android:host="play-gmsd.xyz" />
                <data android:host="gmsd.fun" />
                <data android:host="gmsd.one" />
                <data android:host="gmsd.club" />
                <data android:host="gmsd.pro" />
                <data android:host="gmsd.xyz" />
                <data android:host="bestgmsdeluxe1.org" />
                <data android:host="bestgmsdeluxe2.top" />
                <data android:host="bestgmsdeluxe3.email" />
                <data android:host="bestgmsdeluxe4.club" />
                <data android:host="thegmsd.net" />
                <data android:host="thegmsd.org" />
                <data android:host="thegmsd.com" />
                <data android:host="thegmsd.email" />
                <data android:host="the-gmsd.com" />
                <data android:host="play-gmsd.one" />
                <data android:host="gmsd1.site" />
                <data android:host="mail-gmsd.com" />
                <data android:host="mail-gmsd.email" />
                <data android:host="mail-gmsd.net" />
                <data android:host="mail-gmsd.org" />
                <data android:host="mail-gmsd.win" />
                <data android:host="r-dellyx.com" />
                <data android:host="gmsd.biz" />
                <data android:host="gmsd1.top" />
                <data android:host="r-dellux.com" />
                <data android:host="gmsd1.one" />
                <data android:host="r-de1yx.com" />
                <data android:host="gmslotsmirrors.com" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="gmsd1.gmsdtrack.com" />
                <data android:host="gmsd2.gmsdtrack.com" />
                <data android:host="gmsd3.gmsdtrack.com" />
                <data android:host="gmsd4.gmsdtrack.com" />
                <data android:host="gmsd5.gmsdtrack.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Check Pre-Game Urls -->
                <data android:scheme="app" />
                <data android:host="secretapp10.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="social" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="open"
                    android:host="${REAL_APP_ID}" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.example.general.OPEN_PUSH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.example.error.ui.ErrorActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <service
            android:name=".service.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification" />
    </application>

</manifest>
