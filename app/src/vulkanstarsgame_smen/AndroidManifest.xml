<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter android:label="@string/app_name">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="vss.unsubu.com" />
                <data android:host="www.vss.unsubu.com" />

                <data android:host="unsubu.com" />
                <data android:host="www.unsubu.com" />

                <data android:host="fastredirector.net" />
                <data android:host="www.fastredirector.net" />

                <data android:host="reddirectme.live" />
                <data android:host="www.reddirectme.live" />

                <data android:host="dev.vulkanstars" />
                <data android:host="vu1kanstars.vip" />
                <data android:host="vulkan8stars.club" />
                <data android:host="vulkanstars.best" />
                <data android:host="wulcan2stars.club" />
                <data android:host="starsswulkan.club" />
                <data android:host="wullkanstars.club" />
                <data android:host="wullkanstars.online" />
                <data android:host="vulkanstarrs.online" />
                <data android:host="wulcanstars.best" />

                <data android:host="www.dev.vulkanstars" />
                <data android:host="www.vu1kanstars.vip" />
                <data android:host="www.vulkan8stars.club" />
                <data android:host="www.vulkanstars.best" />
                <data android:host="www.wulcan2stars.club" />
                <data android:host="www.starsswulkan.club" />
                <data android:host="www.wullkanstars.club" />
                <data android:host="www.wullkanstars.online" />
                <data android:host="www.vulkanstarrs.online" />
                <data android:host="www.wulcanstars.best" />

                <data android:host="wulcann7stars.site" />
                <data android:host="www.wulcann7stars.site" />

                <data android:host="wulcann8stars.club" />
                <data android:host="www.wulcann8stars.club" />

                <data android:host="wulcann8stars.online" />
                <data android:host="www.wulcann8stars.online" />

                <data android:host="vwulcanstars.club" />
                <data android:host="www.vwulcanstars.club" />

                <data android:host="vwulcanstars.live" />
                <data android:host="www.vwulcanstars.live" />

                <data android:host="vwulcanstars.online" />
                <data android:host="wwww.vwulcanstars.online" />

                <data android:host="vwulcanstars.site" />
                <data android:host="www.vwulcanstars.site" />

                <data android:host="wuulkanstars.best" />
                <data android:host="www.wuulkanstars.best" />

                <data android:host="wuulkanstars.online" />
                <data android:host="www.wuulkanstars.online" />

                <data android:host="wuulkanstars.site" />
                <data android:host="www.wuulkanstars.site" />

                <data android:host="vulkansstarss.club" />
                <data android:host="www.vulkansstarss.club" />

                <data android:host="vulkansstarss.live" />
                <data android:host="www.vulkansstarss.live" />

                <data android:host="vulkansstarss.online" />
                <data android:host="www.vulkansstarss.online" />

                <data android:host="vulkansstarss.site" />
                <data android:host="www.vulkansstarss.site" />

                <data android:host="vulkan5sstarss.club" />
                <data android:host="www.vulkan5sstarss.club" />

                <data android:host="vulkan5sstarss.live" />
                <data android:host="www.vulkan5sstarss.live" />

                <data android:host="vulkan5sstarss.site" />
                <data android:host="www.vulkan5sstarss.site" />

                <data android:host="vulkann3stars.site" />
                <data android:host="www.vulkann3stars.site" />

                <data android:host="vulkann5stars.online" />
                <data android:host="www.vulkann5stars.online" />

                <data android:host="vulkann5stars.site" />
                <data android:host="www.vulkann5stars.site" />

                <data android:host="vullkanstarrs.site" />
                <data android:host="www.vullkanstarrs.site" />

                <data android:host="vullkan3starrs.club" />
                <data android:host="www.vullkan3starrs.club" />
                <data android:host="redirstars.site" />
                <data android:host="affvipredir1.com" />
                <data android:host="aredirect.club" />
                <data android:host="easyredirect.club" />
                <data android:host="goredirmefast.club" />
                <data android:host="letitstars.fun" />
                <data android:host="letitstars.site" />
                <data android:host="letitstars19.club" />
                <data android:host="letitstars19.com" />
                <data android:host="letitstars7.net" />
                <data android:host="letthisstars.club" />
                <data android:host="mail-stars.email" />
                <data android:host="mail-stars.net" />
                <data android:host="mail-stars.org" />
                <data android:host="mail-stars.win" />
                <data android:host="mail-vstars.com" />
                <data android:host="openredirect.club" />
                <data android:host="perredirect1.com" />
                <data android:host="r-stars6.com" />
                <data android:host="r-stars7.com" />
                <data android:host="re-stars1.com" />
                <data android:host="redirect777.com" />
                <data android:host="redirectforfun.net" />
                <data android:host="redirectmefast.vip" />
                <data android:host="redirectvip.vip" />
                <data android:host="starsredirect.club" />
                <data android:host="vipredir.com" />
                <data android:host="waitingforredirect.me" />
                <data android:host="waitingforredirect.net" />
                <data android:host="wonderfulredirect.net" />
                <data android:host="letitstars7.club" />
                <data android:host="reddirrector.site" />
                <data android:host="starsemail.net" />
                <data android:host="starsemail.org" />
                <data android:host="starsvullcan.net" />
                <data android:host="starsvullcan.org" />
                <data android:host="vullcanstars.net" />
                <data android:host="vullcanstars.org" />
                <data android:host="redirecttoclub.site" />
                <data android:host="redirectvip1.club" />
                <data android:host="vip1redirect.club" />
                <data android:host="vip6redirect.club" />
                <data android:host="vip5redirect.vip" />
                <data android:host="r-stars.org" />
                <data android:host="starsredirect.com" />
                <data android:host="redirectmyredirect.club" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="vstrs1.vstrstrack.com" />
                <data android:host="vstrs2.vstrstrack.com" />
                <data android:host="vstrs3.vstrstrack.com" />
                <data android:host="vstrs4.vstrstrack.com" />
                <data android:host="vstrs5.vstrstrack.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Check Pre-Game Urls -->
                <data android:scheme="app" />
                <data android:host="secretapp28.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="social" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="open"
                    android:host="${REAL_APP_ID}" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.example.general.OPEN_PUSH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.example.error.ui.ErrorActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <service
            android:name=".service.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification" />
    </application>

</manifest>
