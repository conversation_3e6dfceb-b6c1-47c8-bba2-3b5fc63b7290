<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter android:label="@string/app_name">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="vr2.unsubu.com" />
                <data android:host="www.vr2.unsubu.com" />

                <data android:host="vr.unsubu.com" />
                <data android:host="www.vr.unsubu.com" />

                <data android:host="r-vlk777.org" />
                <data android:host="re-vuaroyal.com" />
                <data android:host="clubv.top" />
                <data android:host="mail-royal-777.com" />
                <data android:host="mail-royal-777.email" />
                <data android:host="mail-royal-777.net" />
                <data android:host="mail-royal-777.org" />
                <data android:host="mail-royal-777.win" />
                <data android:host="mail-royal777.com" />
                <data android:host="mail-royal777.email" />
                <data android:host="mail-royal777.net" />
                <data android:host="mail-royal777.org" />
                <data android:host="mail-royal777.win" />
                <data android:host="r-vlk777.net" />
                <data android:host="redir4u.club" />
                <data android:host="r-vlk777.biz" />
                <data android:host="r-vlk777.com" />
                <data android:host="vlkredirect.club" />
                <data android:host="aztoxic.com" />
                <data android:host="redirect4u.club" />
                <data android:host="777-email.com" />
                <data android:host="777-email.net" />
                <data android:host="777-email.org" />
                <data android:host="777-email.win" />
                <data android:host="777-mail.win" />
                <data android:host="777-mail.org" />
                <data android:host="777-mail.net" />
                <data android:host="777-inbox.com" />
                <data android:host="777-inbox.net" />
                <data android:host="777-inbox.org" />
                <data android:host="777-inbox.win" />
                <data android:host="777-inbox.email" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="v777v1.v777vtrack.com" />
                <data android:host="v777v2.v777vtrack.com" />
                <data android:host="v777v3.v777vtrack.com" />
                <data android:host="v777v4.v777vtrack.com" />
                <data android:host="v777v5.v777vtrack.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Check Pre-Game Urls -->
                <data android:scheme="app" />
                <data android:host="secretapp59.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="social" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="open"
                    android:host="${REAL_APP_ID}" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.example.general.OPEN_PUSH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.example.error.ui.ErrorActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <service
            android:name=".service.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification" />
    </application>

</manifest>
