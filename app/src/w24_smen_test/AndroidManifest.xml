<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter android:label="@string/app_name">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="v242.unsubu.com" />
                <data android:host="www.v242.unsubu.com" />

                <data android:host="v24.unsubu.com" />
                <data android:host="www.v24.unsubu.com" />

                <data android:host="24vlk.email" />
                <data android:host="go24.one" />
                <data android:host="justvulkan.com" />
                <data android:host="justvulkan.net" />
                <data android:host="r-clb24.com" />
                <data android:host="r-vul24.com" />
                <data android:host="r-vull24.com" />
                <data android:host="r-vyl24.com" />
                <data android:host="r-vyll24.com" />
                <data android:host="r24v.net" />
                <data android:host="r24v.org" />
                <data android:host="r24v.site" />
                <data android:host="rdr24.com" />
                <data android:host="rdr24.email" />
                <data android:host="rdr24.fun" />
                <data android:host="rdr24.one" />
                <data android:host="rdr24.org" />
                <data android:host="rdr24.top" />
                <data android:host="re-vul24.com" />
                <data android:host="redir24.fun" />
                <data android:host="redir24.one" />
                <data android:host="redir24.org" />
                <data android:host="redir24.top" />
                <data android:host="tryvulkan.com" />
                <data android:host="tryvulkan.net" />
                <data android:host="v24club.fun" />
                <data android:host="v24club.pro" />
                <data android:host="v24me.com" />
                <data android:host="v24me.fun" />
                <data android:host="v24me.org" />
                <data android:host="v24me.top" />
                <data android:host="v24rdr.org" />
                <data android:host="vlk24.email" />
                <data android:host="vlk24.win" />
                <data android:host="vlkan24.club" />
                <data android:host="vlkan24.email" />
                <data android:host="vlkan24.win" />
                <data android:host="vlkn24.email" />
                <data android:host="vlkn24.net" />
                <data android:host="vlkn24.org" />
                <data android:host="vlkn24.top" />
                <data android:host="vlkn24.win" />
                <data android:host="vu1kan24.bet" />
                <data android:host="vu1kan24.biz" />
                <data android:host="vu1kan24.club" />
                <data android:host="vu1kan24.net" />
                <data android:host="vu1kan24.one" />
                <data android:host="vu1kan24.online" />
                <data android:host="vu1kan24.org" />
                <data android:host="vu1kan24.site" />
                <data android:host="vu1kan24.top" />
                <data android:host="vu1kan24.website" />
                <data android:host="vulk24.one" />
                <data android:host="vulkan24.email" />
                <data android:host="red-24.com" />
                <data android:host="24win.pro" />
                <data android:host="mail-vulkan24.com" />
                <data android:host="mail-vulkan24.email" />
                <data android:host="mail-vulkan24.net" />
                <data android:host="mail-vulkan24.org" />
                <data android:host="mail-vulkan24.win" />
                <data android:host="24win.fun" />
                <data android:host="the24vulkan.com" />
                <data android:host="vul24.top" />
                <data android:host="24-vulcan.email" />
                <data android:host="24-vulkan.email" />
                <data android:host="24vulkan.email" />
                <data android:host="the24vulcan.com" />
                <data android:host="the24vulcan.email" />
                <data android:host="the24vulcan.net" />
                <data android:host="the24vulcan.org" />
                <data android:host="the24vulkan.email" />
                <data android:host="the24vulkan.net" />
                <data android:host="the24vulkan.org" />
                <data android:host="thevulcan24.com" />
                <data android:host="thevulcan24.net" />
                <data android:host="re-vull24.com" />
                <data android:host="re-vyl24.com" />
                <data android:host="re-vyll24.com" />
                <data android:host="go24.fun" />
                <data android:host="re-clb24.com" />
                <data android:host="v24-red.appspot.com" />
                <data android:host="vul24.club" />
                <data android:host="vul24.org" />

                <data android:host="vulkan24win.one" />
                <data android:host="vulkan24win.net" />
                <data android:host="vulkan24win.org" />
                <data android:host="vulkan24win.club" />

                <data android:host="www.vulkan24win.one" />
                <data android:host="www.vulkan24win.net" />
                <data android:host="www.vulkan24win.org" />
                <data android:host="www.vulkan24win.club" />
                <data android:host="24win.org" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Check Pre-Game Urls -->
                <data android:scheme="app" />
                <data android:host="secretapp24.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="social" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="open"
                    android:host="${REAL_APP_ID}" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.example.general.OPEN_PUSH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.example.error.ui.ErrorActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <service
            android:name=".service.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification" />
    </application>

</manifest>
