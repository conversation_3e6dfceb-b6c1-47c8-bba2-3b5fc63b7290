<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/app_name"
                tools:targetApi="m">

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="devfront-cv-redirector.hwtool.biz" />
                <data android:host="redirect2club.info" />
            </intent-filter>

            <intent-filter android:label="@string/app_name">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="cv2.unsubu.com" />
                <data android:host="www.cv2.unsubu.com" />

                <data android:host="cv.unsubu.com" />
                <data android:host="www.cv.unsubu.com" />

                <data android:host="newvulkan.net" />
                <data android:host="club4u.pro" />

                <data android:host="bestofvulkan.com" />
                <data android:host="bestofvulkan.net" />
                <data android:host="club-rdr.com" />
                <data android:host="club-vulkan.email" />
                <data android:host="club777.org" />
                <data android:host="clubb.xyz" />
                <data android:host="clubme.fun" />
                <data android:host="clubv.pro" />
                <data android:host="clubvlkn.email" />
                <data android:host="clubvlkn.org" />
                <data android:host="clubvlkn.top" />
                <data android:host="clubvlkn.win" />
                <data android:host="clubvulkan.email" />
                <data android:host="clubvulkan.win" />
                <data android:host="cv-play.com" />
                <data android:host="getvulkan.com" />
                <data android:host="klub-vulkan.email" />
                <data android:host="klubvulkan.email" />
                <data android:host="klyb.rocks" />
                <data android:host="klyb1.com" />
                <data android:host="klyb1.org" />
                <data android:host="livevulkan.com" />
                <data android:host="livevulkan.net" />
                <data android:host="moiclub.net" />
                <data android:host="moiclub.org" />
                <data android:host="moiklyb.com" />
                <data android:host="moiklyb.net" />
                <data android:host="moiklyb.org" />
                <data android:host="r-clubv.com" />
                <data android:host="re-clb.com" />
                <data android:host="re-club.org" />
                <data android:host="re-cv.bet" />
                <data android:host="re-cv.biz" />
                <data android:host="re-cv.club" />
                <data android:host="re-cv.net" />
                <data android:host="re-cv.one" />
                <data android:host="re-cv.rocks" />
                <data android:host="redsendme33.com" />
                <data android:host="thevulkan.email" />
                <data android:host="vlkanclub.email" />
                <data android:host="vlkanclub.net" />
                <data android:host="vlkanclub.top" />
                <data android:host="vlkanclub.win" />
                <data android:host="vuclub.biz" />
                <data android:host="vuclub.club" />
                <data android:host="vuclub.info" />
                <data android:host="vuclub.net" />
                <data android:host="vulclab.xyz" />
                <data android:host="vulkan-club.email" />
                <data android:host="vulkan-klub.email" />
                <data android:host="vulkanclub.email" />
                <data android:host="vulkanclub.win" />
                <data android:host="vulkanslots.email" />
                <data android:host="vulkanwin.email" />
                <data android:host="vulcan1.net" />
                <data android:host="vulclab.pro" />
                <data android:host="clubb.site" />
                <data android:host="clubb.top" />
                <data android:host="klyb.online" />
                <data android:host="mail-vulkan.com" />
                <data android:host="mail-vulkan.email" />
                <data android:host="mail-vulkan.net" />
                <data android:host="mail-vulkan.org" />
                <data android:host="mail-vulkan.win" />
                <data android:host="re-vclb.com" />
                <data android:host="vuclub.top" />
                <data android:host="club4u.site" />
                <data android:host="clubb.biz" />
                <data android:host="clubb.one" />
                <data android:host="clubb.rocks" />
                <data android:host="clubv.fun" />
                <data android:host="klyb.bet" />
                <data android:host="r-c1ubv.com" />
                <data android:host="r-clb3.com" />
                <data android:host="r-k1b.com" />
                <data android:host="r-klybv.com" />
                <data android:host="r-vklb.com" />
                <data android:host="r-wk1b.com" />
                <data android:host="re-c1b.com" />
                <data android:host="re-cv.org" />
                <data android:host="re-k1ybv.com" />
                <data android:host="re-klb7.com" />
                <data android:host="re-klbw3.com" />
                <data android:host="re-klubv.com" />
                <data android:host="re-wc1b.com" />
                <data android:host="re-wklb1.com" />
                <data android:host="c-vulkan.com" />
                <data android:host="c-vulkan.net" />
                <data android:host="c-vulkan.org" />
                <data android:host="club-vulkan.win" />
                <data android:host="clubb.online" />
                <data android:host="clubb.website" />
                <data android:host="clubvulcan.email" />
                <data android:host="clubvulcan.win" />
                <data android:host="clubvulkancom.org" />
                <data android:host="klub-vulcan.com" />
                <data android:host="klub-vulcan.email" />
                <data android:host="klub-vulcan.net" />
                <data android:host="klub-vulcan.org" />
                <data android:host="klubvulcan.com" />
                <data android:host="klubvulcan.email" />
                <data android:host="klubvulcan.net" />
                <data android:host="klubvulcan.org" />
                <data android:host="klyb.website" />
                <data android:host="re-c1ubv.com" />
                <data android:host="vulcanklub.net" />
                <data android:host="vulcanklub.org" />
                <data android:host="vulkancllub.com" />
                <data android:host="vylkanclyb.com" />
                <data android:host="clubb.club" />
                <data android:host="re-cv.site" />
                <data android:host="re-cv.top" />
                <data android:host="re-cv.xyz" />
                <data android:host="vuclub.org" />
                <data android:host="club-r.appspot.com" />
                <data android:host="clubb.bet" />
                <data android:host="clubb.info" />
                <data android:host="cv-red.appspot.com" />
                <data android:host="klyb.info" />
                <data android:host="klyb.one" />
                <data android:host="klyb.site" />
                <data android:host="klyb.xyz" />
                <data android:host="r-klb.com" />
                <data android:host="r-wclb8.com" />
                <data android:host="re-c1bv.com" />
                <data android:host="re-clbv7.com" />
                <data android:host="vulkan-email.com" />
                <data android:host="vulkan-email.org" />
                <data android:host="vulkan-email.net" />
                <data android:host="vulkan-email.win" />
                <data android:host="vulkan-mail.win" />
                <data android:host="vulkan-mail.net" />
                <data android:host="vulkan-mail.org" />
                <data android:host="vulkan-inbox.com" />
                <data android:host="vulkan-inbox.org" />
                <data android:host="vulkan-inbox.net" />
                <data android:host="vulkan-inbox.email" />
                <data android:host="vulkan-inbox.win" />

                <data android:host="vulkan-clubcasino.site" />
                <data android:host="vulkan-cllub.net" />
                <data android:host="vulkan-cllub.bet" />
                <data android:host="vulkan-cllub.top" />
                <data android:host="vulkan-clubcasino.online" />

                <data android:host="www.vulkan-clubcasino.site" />
                <data android:host="www.vulkan-cllub.net" />
                <data android:host="www.vulkan-cllub.bet" />
                <data android:host="www.vulkan-cllub.top" />
                <data android:host="www.vulkan-clubcasino.online" />

                <data android:host="cv.crmtrackings.com" />
                <data android:host="klyb.online" />
                <data android:host="r-cl69.com" />
                <data android:host="r-vc24.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Check Pre-Game Urls -->
                <data android:scheme="app" />
                <data android:host="secretapp7.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="social" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="open" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.example.general.OPEN_PUSH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.example.error.ui.ErrorActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <service
            android:name=".service.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification" />
    </application>

</manifest>
