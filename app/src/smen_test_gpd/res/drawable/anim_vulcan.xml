<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector xmlns:android="http://schemas.android.com/apk/res/android"
            android:width="96dp"
            android:height="96dp"
            android:viewportWidth="48"
            android:viewportHeight="48">
            <path
                android:name="anim_line"
                android:fillColor="#00000000"
                android:pathData="M29.6206,22.8543L11.4027,45.5068C8.3147,49.3465 2.3681,45.2936 4.8127,41.0154L26.527,3.0154C28.2554,-0.0091 32.7555,0.4676 33.8117,3.7872L40.8117,25.7872C42.2321,30.2512 36.1404,33.0997 33.6254,29.1475L29.6206,22.8543Z"
                android:strokeWidth="2"
                android:strokeColor="#38C4FF"
                android:strokeLineCap="round" />
            <path
                android:fillColor="#FF0000"
                android:pathData="M8.2857,43l21.7143,-27l7,11l-7,-22z" />
        </vector>
    </aapt:attr>
    <target android:name="anim_line">
        <aapt:attr name="android:animation">
            <set xmlns:android="http://schemas.android.com/apk/res/android">
                <objectAnimator
                    android:duration="1500"
                    android:propertyName="trimPathStart"
                    android:valueFrom="1"
                    android:valueTo="0"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
</animated-vector>
