<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
    </queries>

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/AppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="vp" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />

                <data android:host="track.platinum-vulk-emails.com" />
                <data android:host="track.platinum-vulk-email.com" />
                <data android:host="track.platinum-vulkemail.com" />
                <data android:host="track.platinumvulkemail.com" />
                <data android:host="alter.trackhub.net" />
                <data android:host="track.emails-vulkplatinum.com" />
                <data android:host="track.emailvulk-platinum.com" />
                <data android:host="track.email-vulkplatinum.com" />
                <data android:host="track.email-vulk-platinum.com" />
                <data android:host="track.emailsvulkplatinum.com" />
                <data android:host="track.mailvulkplatinum.com" />
                <data android:host="track.emailvulkplatinum.com" />
                <data android:host="track.vulkplatinum.com" />
                <data android:host="track.vulplatina.com" />
                <data android:host="track.vulkanplatina.com" />
                <data android:host="track.vulkplatina.com" />
                <data android:host="track.emailvulkplatina.com" />
                <data android:host="track.mailvulkplatina.com" />
                <data android:host="track.emailsvulkplatina.com" />
                <data android:host="track.email-vulk-platina.com" />
                <data android:host="track.email-vulkplatina.com" />
                <data android:host="track.emailvulk-platina.com" />
                <data android:host="track.emails-vulk-platina.com" />
                <data android:host="track.emailsvulk-platina.com" />
                <data android:host="track.emails-vulkplatina.com" />
                <data android:host="track.platinavulkemail.com" />
                <data android:host="track.platina-vulk-email.com" />
                <data android:host="track.platina-vulk-emails.com" />
                <data android:host="track.vulplatinym.com" />
                <data android:host="track.vulkanplatinym.com" />
                <data android:host="track.vulkplatinym.com" />
                <data android:host="track.emailvulkplatinym.com" />
                <data android:host="track.mailvulkplatinym.com" />
                <data android:host="track.emailsvulkplatinym.com" />
                <data android:host="track.email-vulk-platinym.com" />
                <data android:host="track.email-vulkplatinym.com" />
                <data android:host="track.emailvulk-platinym.com" />
                <data android:host="track.emails-vulk-platinym.com" />
                <data android:host="track.emailsvulk-platinym.com" />
                <data android:host="track.emails-vulkplatinym.com" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                tools:targetApi="m">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />

                <data android:host="click1.track-platinum.com" />
                <data android:host="click2.track-platinum.com" />
                <data android:host="click3.track-platinum.com" />
                <data android:host="click4.track-platinum.com" />
                <data android:host="click5.track-platinum.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Check Pre-Game Urls -->
                <data android:scheme="app" />
                <data android:host="secretapp84.com" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${REAL_APP_ID}"
                    android:scheme="social" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="open"
                    android:host="${REAL_APP_ID}" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.example.general.OPEN_PUSH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.example.error.ui.ErrorActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/AppTheme" />

        <service
            android:name=".service.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification" />
    </application>

</manifest>
