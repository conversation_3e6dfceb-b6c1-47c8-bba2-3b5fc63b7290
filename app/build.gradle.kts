import com.android.build.api.dsl.ProductFlavor

plugins {
    alias(libs.plugins.androidApplication)
    alias(libs.plugins.jetbrainsKotlinAndroid)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.kotlin.ksp)
    alias(libs.plugins.google.services)
    alias(libs.plugins.firebase.crashlytics)
}

android {
    namespace = "com.example.general"
    ndkVersion = "26.1.10909125"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.example.general"
        minSdk = 21
        targetSdk = 35
        versionCode = 247
        versionName = "100.24"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    buildTypes {
        debug {
            isMinifyEnabled = true

            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }

        release {
            isMinifyEnabled = true
            isShrinkResources = true

            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    flavorDimensions.add("app")
    productFlavors {
        create("apk_testing") {
            applicationId = "id.to.test"
            addIDConstant(this, "REAL_APP_ID", "test.to.id")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f505-1051-60be-933d-fbc727477af6\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://devfront-phar-redirector.hwtool.biz/redirect/?uri=\""
            )

        }

        //Wave 0
        // SMEN:
        create("cv_smen_test") { // CV_TEST
            applicationId = "com.clubvulkan.test"
            addIDConstant(this, "REAL_APP_ID", "test.clubvulkan.com")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f418-6356-672a-b486-a35cce5c6a84\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://cv.redirectors.dev/redirect/?uri=\""
            )
        }

        create("phar_smen_test") { // PH_TEST
            applicationId = "com.pharaon.test"
            addIDConstant(this, "REAL_APP_ID", "test.pharaon.com")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f2fb-52ad-64f0-a030-c5bb72e56b72\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://ph.redirectors.dev/redirect/?uri=\""
            )
        }

        create("w24_smen_test") { // V24_TEST
            applicationId = "com.vulkan24.test"
            addIDConstant(this, "REAL_APP_ID", "test.vulkan24.com")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f468-b013-6fe0-9bfb-7b9ec5f35c95\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://v24.redirectors.dev/redirect/?uri=\""
            )
        }

        create("smen_test_gpd") { // SMEN_TEST_GPD
            applicationId = "com.testapp.gpd"
            addIDConstant(this, "REAL_APP_ID", "com.testapp.gpd")
            buildConfigField("String", "APP_TOKEN", "\"1f019346-44c6-6fb4-8ddc-330dda8ff051\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://cv.redirectors.dev/redirect/?uri=\""
            )
        }

        create("smen_test_cis") { // CV_TEST_CIS
            applicationId = "com.testapp.cis"
            addIDConstant(this, "REAL_APP_ID", "com.testapp.cis")
            buildConfigField("String", "APP_TOKEN", "\"1f0193ba-f7aa-6c2e-af48-a590eb6c0419\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://cv.redirectors.dev/redirect/?uri=\""
            )
        }

        // GI:
        create("slotoro_test") { // SLOTORO_TEST
            applicationId = "com.slotoro.test"
            addIDConstant(this, "REAL_APP_ID", "test.slotoro.com")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f473-ae03-6c7c-9a56-cfe75ca2de2c\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://awi-dev-redirector-05.hwtool.biz/?pm=1&uri=\""
            )
        }

        // YS:
        create("mr_bet_ys_test") { // MB_YS_TEST
            applicationId = "com.mbandroid.test"
            addIDConstant(this, "REAL_APP_ID", "ys.productwv.index000.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f47b-679d-62b2-987d-71eda4fc659d\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://mtnbt.com/zmLfX\""
            )

            // supports English, French, Norwegian, Finnish, German, Spanish, Portuguese (Portugal), Portuguese (Brazil), Japanese, Polish
            resourceConfigurations.addAll(
                listOf("en", "fr", "no", "de", "es", "pt", "pt-rBR", "ja", "pl")
            )
        }

        //Wave 1
        // SMEN:
        create("gms_smen") { //GMSD
            applicationId = "com.gmsdeluxe.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index10.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f7cf-a4ee-652e-bc5c-e711685bb2bd\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect27.com/?pm=1&uri=\""
            )
        }

        create("gmsl_smen") { //GMSL
            applicationId = "com.gmsl.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index1.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f7d5-6454-6d1e-89e0-9d5dfa121c0a\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect25.com/?pm=1&uri=\""
            )
        }

        create("masal_bt") { //MBET
            applicationId = "com.productwv.index6723.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index6723.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef21c49-e300-6712-a037-ef3014f76b0f\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://237redirect.com/?pm=1&uri=\""
            )
        }

        create("phar_smen") { //PH
            applicationId = "com.pharaon.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index15.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f7dc-fb69-6a54-9c26-290db284f1df\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect33.com/?pm=1&uri=\""
            )
        }

        create("sea_star_casino") { //SSTAR
            applicationId = "com.productwv.index158.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index158.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef14470-37d6-6cbc-b6c4-3f0f5d987b81\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://177redirect.com/?pm=1&uri=\""
            )
        }

        create("vox_casino") { //VOX
            applicationId = "com.productwv.index173.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index173.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef16b9c-5be8-6314-95f4-5fd0321d8e6c\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://182redirect.com/?pm=1&uri=\""
            )
        }

        create("vulkanua_smen") { //V777
            applicationId = "com.vulkanua.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index59.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f7e7-89b4-6a20-bf37-09f85e2545d7\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://12redirect.com/?pm=1&uri=\""
            )
        }

        create("vulkan_stavka_smen") { //STAV
            applicationId = "app.vulkan.stavka"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index13185.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f7f4-8364-6fdc-bb75-b3dc00a5701f\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect73.com/?pm=1&uri=\""
            )
        }

        //Wave 2
        // SMEN:
        create("abe_bet") { //ABE
            applicationId = "com.productwv.index67183.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index67183.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef4f4e9-7585-60fe-a24c-b79e21503b3e\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://430redirect.com/?pm=1&uri=\""
            )
        }

        create("awintura_smen") { //AWI
            applicationId = "com.awintura.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index76.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef5df94-f8b7-6814-8d64-bd17ef5509d9\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect89.com/?pm=1&uri=\""
            )
        }

        create("basari") { //BSR
            applicationId = "com.productwv.index155.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index155.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef5ee65-54fd-6d74-90c9-d773dc938902\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://21redirect.com/?pm=1&uri=\""
            )
        }

        create("eld_smen") { //EL
            applicationId = "com.eldorado.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index25.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f80a-b46a-671a-8d06-3b67c93fe45f\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect29.com/?pm=1&uri=\""
            )
        }

        create("joy_smen") { //JOY
            applicationId = "com.joykasino.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index30.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f810-f55d-699c-84cc-1d28fb9185f0\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect31.com/?pm=1&uri=\""
            )
        }

        create("league_of_slots") { //LOS
            applicationId = "com.productwv.index72.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index72.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef11de8-d071-6322-a5c4-c9767eeba006\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://171redirect.com/?pm=1&uri=\""
            )
        }

        create("maxbet") { //MBS
            applicationId = "net.maxbetslots.casino"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index2.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f816-a226-6c00-a580-09fbb2e0ae4e\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://221redirect.com/?pm=1&uri=\""
            )
        }

        create("merhabet") { //MHB
            applicationId = "com.productwv.index72249.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index72249.app")
            buildConfigField("String", "APP_TOKEN", "\"1f02507c-b275-6454-ab20-37dfa1a60274\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect499.com/?pm=1&uri=\""
            )
        }

        create("royal_smen") { //RC - Royal Casino ex. VR Vulkan Royal - OLD_ID: com.vulkanroyal.app
            applicationId = "com.productwv.index33.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index33.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f824-17d2-6ae6-a387-69189756cc77\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect37.com/?pm=1&uri=\""
            )
        }

        create("seven_k") { //7K
            applicationId = "com.sevenk.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index79.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f829-5b68-6512-b170-f3de6dbb5017\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect463.com/?pm=1&uri=\""
            )
        }

        create("slotozal") { //SZ
            applicationId = "net.slotozal.casino"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index3.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f82e-78fa-64a4-bfee-870f51fdcda6\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect75.com/?pm=1&uri=\""
            )
        }

        create("vulkan_rus_ys") { //RR - Royal Russia ex. VRUS_YS - OLD_ID: com.vulkanrussia.game
            applicationId = "com.productwv.index84.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index84.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9ff41-0956-6a66-8a90-712ef80fc794\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://155redirect.com/?pm=1&uri=\""
            )
        }

        create("vulkanonline_smen") { //ROC - Rubin Online Casino ex. CVO Vulkan Online - OLD_ID: com.vulkanonline.app
            applicationId = "com.productwv.index13.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index13.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9fef4-8a95-6caa-9109-a9bc53cf5c4e\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect23.com/?pm=1&uri=\""
            )
        }

        create("vulkanstarsgame_smen") { //VS
            applicationId = "com.vulkanstarsgame.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index28.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f834-844d-6f94-82da-897cf9c0a6f0\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect45.com/?pm=1&uri=\""
            )
        }

        create("winnita") { //WINN
            applicationId = "com.productwv.index159.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index159.app")
            buildConfigField("String", "APP_TOKEN", "\"1eef3510-1ecc-6832-8092-91d067c97caf\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect93.com/?pm=1&uri=\""
            )
        }

        create("vdeluxe_smen") { //ON-X ex. Vulkan Deluxe (VD)
            applicationId = "com.vdeluxe.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index32.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9f7e2-3f7c-6340-ab2b-7f895237f7b6\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://457redirect.com/?pm=1&uri=\""
            )
        }

        create("maggico_smen") { //MaGGico
            applicationId = "com.productwv.index76187.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index76187.app")
            buildConfigField("String", "APP_TOKEN", "\"1efbd5fb-c8d9-64f4-b09a-57d86cd14db6\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://27redirect.com/?pm=1&uri=\""
            )
        }

        // GI:
        create("fortunica") { //Fortunica
            applicationId = "com.productwv.index93.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index93.app")
            buildConfigField("String", "APP_TOKEN", "\"1f03017f-ac31-6f0c-b30e-b386b3e4fe7b\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect46.com/?pm=1&uri=\""
            )
        }

        create("slotoro") { //SLOTORO
            applicationId = "com.slotoro.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index78.app")
            buildConfigField("String", "APP_TOKEN", "\"1eefbfbd-3f27-efbf-bdef-bfbd6e12efbf\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect91.com/?pm=1&uri=\""
            )
        }

        create("vulkan_bet_com") { //VBET (офшор, мультиверсии)
            applicationId = "app.vk_bet.com"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index57.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef5ee6f-2e13-6768-985e-1bf9a8db5f08\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect51.com/?pm=1&uri=\""
            )
        }

        create("vulkan_dot_bet") { // VBET WP
            applicationId = "vulkan.bet"
            addIDConstant(this, "REAL_APP_ID", "com.wpwv.index57.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef5ee54-be50-676a-be0b-afa667b1b7a3\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://mysweet-profit.com/?s=66\""
            )
        }

        //Wave 3
        // SMEN:
        create("cv_smen") { //CV
            applicationId = "com.clubvulkan.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index7.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9ff18-a3fa-61fe-b439-0b5b7770a462\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://153redirect.com/?pm=1&uri=\""
            )
        }

        create("ssl_smen") { //SSL
            applicationId = "com.seven_slots.apl"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index67.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef5ee7e-fe37-6796-81b3-ab3c304e89fb\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect81.com/?pm=1&uri=\""
            )
        }

        create("vip_royal_smen") { //VIPR
            applicationId = "com.productwv.index19178.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index19178.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef693b1-fbc7-69f6-82d3-7f42de7a0831\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://595redirect.com/?pm=1&uri=\""
            )
        }

        create("vip_smen") { //VIP
            applicationId = "com.vip.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index19.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9ff20-6f71-6ee8-883d-55adcfb0493f\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://go2clb.news/?pm=1&uri=\""
            )
        }

        create("vulkan_platinum_ys") { //VP - Platinum Casino ex. Vulkan Platinum YS
            applicationId = "com.androidapp.vp"
            addIDConstant(this, "REAL_APP_ID", "com.androidapp.vp")
            buildConfigField("String", "APP_TOKEN", "\"1ee9ff36-b755-6866-9259-6d95bd70c0b7\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect433.com/?pm=1&uri=\""
            )
        }

        create("w24_smen") { //V24
            applicationId = "com.vulkan24.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index24.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9ff28-9de9-6b6a-9d0f-21d239fe9de9\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://go-win.pro/?pm=1&uri=\""
            )
        }

        // GI:
        create("ggbet_smen") { // GG
            applicationId = "com.ggbet.app"
            addIDConstant(this, "REAL_APP_ID", "com.productwv.index29.app")
            buildConfigField("String", "APP_TOKEN", "\"1ef5ee76-f302-6a0e-a5ca-f1bfc6b6c57f\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://redirect2win.pro/?uri=\""
            )
        }

        // Wave 4 YS
        // subwave 1
        create("bruce_bet_ys") { // BRUCE_BET_YS
            applicationId = "com.bruce.bet"
            addIDConstant(this, "REAL_APP_ID", "ys.productwv.index007.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9ff31-120f-6bae-8973-039cefcdcda5\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://brc.bet/p6Eyw\""
            )

            // supports only English and German languages
            resourceConfigurations.addAll(listOf("en", "de"))
        }

        create("spin_city_ys") { // SC_YS
            applicationId = "apk.spincity.com"
            addIDConstant(this, "REAL_APP_ID", "ys.productwv.index002.app")
            buildConfigField("String", "APP_TOKEN", "\"1ee9ff98-b912-6a98-8e35-c7ca6518c333\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://wa-sc.com/PZdLX\""
            )

            // supports only English, German, Spanish, French, Polish, Portuguese (Portugal), Portuguese (Brazil), Russian languages
            resourceConfigurations.addAll(
                listOf("en", "de", "es", "fr", "pl", "pt", "pt-rBR", "ru")
            )
        }

        create("syndicate_ys") { // SYND_YS
            applicationId = "com.syndi.app"
            addIDConstant(this, "REAL_APP_ID", "com.syndi.app")
            buildConfigField("String", "APP_TOKEN", "\"1eedae67-59d8-6cc2-b239-f78a23deec53\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://tg-sc.com/ivzmK\""
            )

            // supports only English, German, Finnish, French, Norwegian languages
            resourceConfigurations.addAll(listOf("en", "de", "fi", "fr", "no"))
        }

        create("xon_ys") { // XON_YS
            applicationId = "com.xon.bet"
            addIDConstant(this, "REAL_APP_ID", "com.xon.bet")
            buildConfigField("String", "APP_TOKEN", "\"1efacd21-d77a-641a-9305-358239094a3d\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://x-n.bet/34XRb\""
            )

            // supports only English and German languages
            resourceConfigurations.addAll(listOf("en", "de"))
        }

        create("mr_bet_14_ys") { // MB_14_YS
            applicationId = "com.mbandroid14.app"
            addIDConstant(this, "REAL_APP_ID", "com.mbandroid14.app")
            buildConfigField("String", "APP_TOKEN", "\"1efae54b-7fe4-60ce-9bff-7566adb4a0a8\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://czmbs.com/NsBmY\""
            )

            // supports English, French, Norwegian, Finnish, German, Spanish, Portuguese (Portugal), Portuguese (Brazil), Japanese, Polish
            resourceConfigurations.addAll(
                listOf("en", "fr", "no", "fi", "de", "es", "pt", "pt-rBR", "ja", "pl")
            )
        }

        create("mr_bet_15_ys") { // MB_15_YS
            applicationId = "com.mbandroid15.app"
            addIDConstant(this, "REAL_APP_ID", "com.mbandroid15.app")
            buildConfigField("String", "APP_TOKEN", "\"1efae711-064d-6b76-8c11-b751ebf6ff33\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://czmbs.com/lNBwE\""
            )

            // supports English, French, Norwegian, Finnish, German, Spanish, Portuguese (Portugal), Portuguese (Brazil), Japanese, Polish
            resourceConfigurations.addAll(
                listOf("en", "fr", "no", "fi", "de", "es", "pt", "pt-rBR", "ja", "pl")
            )
        }

        create("mr_bet_16_ys") { // MB_16_YS
            applicationId = "com.mbandroid16.app"
            addIDConstant(this, "REAL_APP_ID", "com.mbandroid16.app")
            buildConfigField("String", "APP_TOKEN", "\"1efae71a-36c8-6d1a-bd63-af6b000d9305\"")
            buildConfigField(
                "String",
                "EXCEPTION_HANDLER_URL_KEY",
                "\"https://czmbs.com/UvG0l\""
            )

            // supports English, French, Norwegian, Finnish, German, Spanish, Portuguese (Portugal), Portuguese (Brazil), Japanese, Polish
            resourceConfigurations.addAll(
                listOf("en", "fr", "no", "fi", "de", "es", "pt", "pt-rBR", "ja", "pl")
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
        }
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
        resources.pickFirsts.add("**/libnative-lib-app.so")
        resources.pickFirsts.add("**/libnative-lib.so")
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(project(path = ":data"))
    implementation(project(path = ":domain"))
    implementation(project(path = ":common"))
    implementation(project(path = ":ui-common"))
    implementation(project(path = ":feature-web"))
    implementation(project(path = ":feature-error-handler"))
    implementation(project(path = ":feature-lost-network"))

    // Lifecycle
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.ktx.viewmodel)
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.lifecycle.extensions)

    // Compose
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)

    // Navigation compose
    implementation(libs.navigation.compose)

    // Coroutines
    implementation(libs.coroutines.core)
    implementation(libs.coroutines.android)

    // Koin
    implementation(platform(libs.koin.bom))
    implementation(libs.koin.android)
    implementation(libs.koin.composables)
    implementation(libs.koin.workmanager)

    // DataStore
    implementation(libs.data.store.prefs.android)
    implementation(libs.play.services.analytics)

    // Room
    implementation(libs.room.ktx)
    implementation(libs.room.runtime)
    annotationProcessor(libs.room.compiler)
    ksp(libs.room.compiler)

    // Glide
    implementation(libs.glide)

    // Retrofit
    implementation(libs.retrofit)
    implementation(libs.retrofit.gson)

    // WorkManager
    implementation(libs.androidx.work.runtime.ktx)

    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.crashlytics)
    implementation(libs.firebase.analytics)
    implementation(libs.firebase.messaging)
    implementation(libs.firebase.config.ktx)

    // Crypto-AES256
    implementation(libs.lib.crypto.aes256)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    androidTestImplementation(libs.androidx.junit.ktx)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}

fun addIDConstant(flavor: ProductFlavor, constantName: String, constantValue: String) {
    flavor.manifestPlaceholders[constantName] = constantValue
    flavor.buildConfigField("String", constantName, "\"$constantValue\"")
}
