package com.example.data.mappers

import com.example.common.exceptions.AppConfigInvalidException
import com.example.common.logger.Logger.log
import com.example.data.models.config.AssetAppConfig
import com.example.data.models.config.AssetParams
import com.example.data.models.config.BuildConfigDataDTO
import com.example.data.models.config.LocalAppConfig
import com.example.data.models.config.LocalParams
import com.example.data.models.config.RemoteAppConfig
import com.example.data.models.config.RemoteParams
import com.example.data.utils.Utils.parseJsonArray
import com.example.domain.models.AppConfig
import com.example.domain.models.Params
import com.example.domain.models.Realisation

internal fun AssetAppConfig.toAppConfig(buildConfigData: BuildConfigDataDTO): AppConfig {
    val logic = when {
        link.isNullOrEmpty() && !params.smenUrl.isNullOrEmpty() -> {
            Realisation.SMEN_GI.name
        }

        link.isNullOrEmpty() && !params.ysUrl.isNullOrEmpty() -> {
            Realisation.YS.name
        }

        !link.isNullOrEmpty() -> {
            Realisation.WP.name
        }

        else -> throw AppConfigInvalidException()
    }

    val url = when (logic) {
        Realisation.SMEN_GI.name -> params.smenUrl
        Realisation.YS.name -> params.ysUrl
        Realisation.WP.name -> link
        else -> params.smenUrl
    } ?: buildConfigData.exceptionHandlerUrlKey

    val result = AppConfig(
        hash = hash,
        image = image,
        domains = domains,
        updaterDomains = updaterDomains,
        downloadCode = downloadCode,
        params = params.toParams(),
        ref = ref,
        uuid = uuid,
        affData = affData,
        salt = salt,
        appInstallUuid = appInstallUuid,
        postfix = postfix,
        link = link,
        url = url,
        logic = logic,
        appToken = buildConfigData.appToken,
        applicationId = buildConfigData.realAppId,
        applicationVersion = buildConfigData.versionName,
        isDebug = buildConfigData.isDebug
    )

    log("AppConfiguration parse from assets result = $result")
    return result
}

internal fun AssetParams.toParams() = Params(
    smenUrl = smenUrl,
    ysUrl = ysUrl,
    ysDomains = ysDomains.parseJsonArray()
)

internal fun LocalAppConfig.toAppConfig(buildConfigData: BuildConfigDataDTO): AppConfig {
    val logic = when {
        link.isNullOrEmpty() && !params.smenUrl.isNullOrEmpty() -> {
            Realisation.SMEN_GI.name
        }

        link.isNullOrEmpty() && !params.ysUrl.isNullOrEmpty() -> {
            Realisation.YS.name
        }

        !link.isNullOrEmpty() -> {
            Realisation.WP.name
        }

        else -> throw AppConfigInvalidException()
    }

    val url = when (logic) {
        Realisation.SMEN_GI.name -> params.smenUrl
        Realisation.YS.name -> params.ysUrl
        Realisation.WP.name -> link
        else -> params.smenUrl
    } ?: buildConfigData.exceptionHandlerUrlKey

    val result = AppConfig(
        hash = hash,
        image = image,
        domains = domains,
        updaterDomains = updaterDomains,
        downloadCode = downloadCode,
        params = params.toParams(),
        ref = ref,
        uuid = uuid,
        affData = affData,
        salt = salt,
        appInstallUuid = appInstallUuid,
        postfix = postfix,
        link = link,
        url = url,
        userToken = userToken.orEmpty(),
        logic = logic,
        fireBaseToken = firebaseToken.orEmpty(),
        appToken = buildConfigData.appToken,
        applicationId = buildConfigData.realAppId,
        applicationVersion = buildConfigData.versionName,
        isDebug = buildConfigData.isDebug
    )

    log("AppConfiguration parse from local result = $result")
    return result
}

internal fun AppConfig.toLocalAppConfig(): LocalAppConfig {
    return LocalAppConfig(
        hash = this.hash,
        image = this.image,
        domains = this.domains,
        updaterDomains = updaterDomains,
        downloadCode = this.downloadCode,
        params = this.params.toLocalParams(),
        ref = this.ref,
        uuid = this.uuid,
        affData = this.affData,
        salt = this.salt,
        appInstallUuid = this.appInstallUuid,
        postfix = this.postfix,
        link = this.link,
        userToken = this.userToken,
        firebaseToken = this.fireBaseToken,
        logic = this.logic
    )
}

internal fun LocalParams.toParams() = Params(
    smenUrl = smenUrl,
    ysUrl = ysUrl,
    ysDomains = ysDomains
)

internal fun Params.toLocalParams() = LocalParams(
    smenUrl = smenUrl,
    ysUrl = ysUrl,
    ysDomains = ysDomains
)

internal fun RemoteAppConfig.mergeWithLocalAppConfig(currentAppConfig: AppConfig): AppConfig {
    return currentAppConfig.copy(
        image = image.takeIf { it.isNotEmpty() } ?: currentAppConfig.image,
        domains = domains.takeIf { !it.isNullOrEmpty() } ?: currentAppConfig.domains,
        updaterDomains = updaterDomains.takeIf { !it.isNullOrEmpty() }
            ?: currentAppConfig.updaterDomains,
        downloadCode = downloadCode.takeIf { it.isNotEmpty() } ?: currentAppConfig.downloadCode,
        params = params?.toParams(currentAppConfig.params) ?: currentAppConfig.params,
        ref = ref.takeIf { !it.isNullOrEmpty() } ?: currentAppConfig.ref,
        uuid = uuid.takeIf { !it.isNullOrEmpty() } ?: currentAppConfig.uuid,
        affData = affData.takeIf { !it.isNullOrEmpty() } ?: currentAppConfig.affData,
        salt = salt.takeIf { !it.isNullOrEmpty() } ?: currentAppConfig.salt,
        appInstallUuid = appInstallUuid.takeIf { !it.isNullOrEmpty() }
            ?: currentAppConfig.appInstallUuid,
        postfix = postfix.takeIf { !it.isNullOrEmpty() } ?: currentAppConfig.postfix,
        link = link.takeIf { !it.isNullOrEmpty() } ?: currentAppConfig.link,
        logic = calculateLogic() ?: currentAppConfig.logic
    )
}

internal fun RemoteParams.toParams(currentParams: Params) = Params(
    smenUrl = smenUrl.takeIf { !it.isNullOrEmpty() } ?: currentParams.smenUrl,
    ysUrl = ysUrl.takeIf { !it.isNullOrEmpty() } ?: currentParams.ysUrl,
    ysDomains = ysDomains.parseJsonArray()
)

private fun RemoteAppConfig.calculateLogic(): String? {
    return when {
        link.isNullOrEmpty() && !params?.smenUrl.isNullOrEmpty() -> {
            Realisation.SMEN_GI.name
        }

        link.isNullOrEmpty() && !params?.ysUrl.isNullOrEmpty() -> {
            Realisation.YS.name
        }

        !link.isNullOrEmpty() -> {
            Realisation.WP.name
        }

        else -> {
            null
        }
    }
}
